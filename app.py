import streamlit as st
from llama_index.embeddings.fastembed import FastEmbedEmbedding
from llama_index.core import Settings
from llama_index.vector_stores.faiss import FaissVectorStore
from llama_index.core import VectorStoreIndex, StorageContext, load_index_from_storage
from llama_index.llms.gemini import Gemini
from llama_index.core.prompts import PromptTemplate
from llama_index.core.memory import Memory
from llama_index.core.llms import ChatMessage
from llama_index.core.storage.chat_store import SimpleChatStore
import faiss
import os
import json
import uuid
from datetime import datetime

# Page configuration
st.set_page_config(
    page_title="Ayurvedic Health Assistant",
    page_icon="🌿",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Memory and chat management functions
def load_chat_store():
    """Load or create chat store for persistent memory"""
    try:
        if os.path.exists("chat_store.json"):
            return SimpleChatStore.from_persist_path("chat_store.json")
        else:
            return SimpleChatStore()
    except:
        return SimpleChatStore()

def save_chat_store(chat_store):
    """Save chat store to disk"""
    try:
        chat_store.persist("chat_store.json")
    except Exception as e:
        st.error(f"Failed to save chat history: {e}")

def load_chat_sessions():
    """Load chat sessions metadata"""
    try:
        if os.path.exists("chat_sessions.json"):
            with open("chat_sessions.json", "r") as f:
                return json.load(f)
        else:
            return {}
    except:
        return {}

def save_chat_sessions(sessions):
    """Save chat sessions metadata"""
    try:
        with open("chat_sessions.json", "w") as f:
            json.dump(sessions, f, indent=2)
    except Exception as e:
        st.error(f"Failed to save sessions: {e}")

def create_new_session():
    """Create a new chat session"""
    session_id = str(uuid.uuid4())
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    return {
        "id": session_id,
        "title": "New Chat",
        "created_at": timestamp,
        "last_updated": timestamp
    }

def update_session_title(sessions, session_id, first_message):
    """Update session title based on first message"""
    if session_id in sessions:
        # Use first 50 characters of the message as title
        title = first_message[:50] + "..." if len(first_message) > 50 else first_message
        sessions[session_id]["title"] = title
        sessions[session_id]["last_updated"] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

# Custom CSS for WhatsApp-style UI with BLACK text and sidebar
st.markdown("""
<style>
    /* Hide Streamlit branding */
    #MainMenu {visibility: hidden;}
    footer {visibility: hidden;}
    header {visibility: hidden;}

    /* Sidebar styling */
    .css-1d391kg {
        background-color: #f0f2f6;
        border-right: 1px solid #e0e0e0;
    }

    /* Sidebar content */
    .sidebar-content {
        padding: 1rem;
    }

    /* Chat session item */
    .chat-session {
        background: white;
        border-radius: 8px;
        padding: 12px;
        margin-bottom: 8px;
        border: 1px solid #e0e0e0;
        cursor: pointer;
        transition: all 0.2s ease;
    }

    .chat-session:hover {
        background: #f5f5f5;
        border-color: #075e54;
    }

    .chat-session.active {
        background: #dcf8c6;
        border-color: #075e54;
    }

    .session-title {
        font-weight: 500;
        color: #333;
        font-size: 14px;
        margin-bottom: 4px;
    }

    .session-date {
        font-size: 12px;
        color: #666;
    }

    /* New chat button */
    .new-chat-btn {
        background: linear-gradient(135deg, #075e54 0%, #128c7e 100%);
        color: white;
        border: none;
        border-radius: 8px;
        padding: 12px;
        width: 100%;
        margin-bottom: 16px;
        cursor: pointer;
        font-weight: 500;
        transition: all 0.2s ease;
    }

    .new-chat-btn:hover {
        background: linear-gradient(135deg, #128c7e 0%, #075e54 100%);
        transform: translateY(-1px);
    }

    /* Main container styling */
    .stApp {
        background-color: #e5ddd5;
        background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23d4d4d4' fill-opacity='0.2'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
    }
    
    /* Chat header */
    .chat-header {
        background: linear-gradient(135deg, #075e54 0%, #128c7e 100%);
        color: white;
        padding: 20px;
        border-radius: 10px 10px 0 0;
        display: flex;
        align-items: center;
        gap: 15px;
        box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        margin-bottom: 20px;
    }
    
    .header-avatar {
        width: 50px;
        height: 50px;
        background: #25d366;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 28px;
    }
    
    .header-info h2 {
        margin: 0;
        font-size: 20px;
        font-weight: 600;
        color: white !important;
    }
    
    .header-info p {
        margin: 0;
        font-size: 14px;
        opacity: 0.9;
        color: #e8f5e9 !important;
    }
    
    /* Force ALL text to be black in chat messages */
    .stChatMessage * {
        color: #000000 !important;
    }
    
    .stChatMessage p,
    .stChatMessage div,
    .stChatMessage span,
    .stChatMessage li,
    .stChatMessage ol,
    .stChatMessage ul {
        color: #000000 !important;
        font-size: 15px !important;
        line-height: 1.5 !important;
    }
    
    /* User messages */
    div[data-testid="user-message"] {
        background-color: #dcf8c6 !important;
        border-radius: 15px 15px 0 15px !important;
        margin-left: 20% !important;
        margin-bottom: 15px !important;
        padding: 12px 16px !important;
        box-shadow: 0 1px 2px rgba(0,0,0,0.1) !important;
    }
    
    /* Assistant messages */
    div[data-testid="assistant-message"] {
        background-color: #ffffff !important;
        border-radius: 15px 15px 15px 0 !important;
        margin-right: 20% !important;
        margin-bottom: 15px !important;
        padding: 12px 16px !important;
        box-shadow: 0 1px 2px rgba(0,0,0,0.1) !important;
    }
    
    /* Avatar styling */
    .stChatMessage img {
        width: 35px !important;
        height: 35px !important;
        margin-top: 5px !important;
    }
    
    /* Chat input styling */
    .stChatInputContainer {
        background-color: #f0f0f0;
        border-radius: 30px;
        padding: 8px;
        margin: 20px 0;
        box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    }
    
    .stChatInputContainer > div {
        background-color: white !important;
        border-radius: 25px !important;
        border: 1px solid #e0e0e0 !important;
    }
    
    .stChatInputContainer textarea {
        color: #000000 !important;
        font-size: 15px !important;
    }
    
    /* Typing indicator */
    .typing-indicator {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 10px;
        color: #667781;
        font-size: 14px;
    }
    
    .typing-dots {
        display: flex;
        gap: 4px;
    }
    
    .typing-dots span {
        width: 8px;
        height: 8px;
        background-color: #667781;
        border-radius: 50%;
        animation: typing 1.4s infinite;
    }
    
    .typing-dots span:nth-child(2) {
        animation-delay: 0.2s;
    }
    
    .typing-dots span:nth-child(3) {
        animation-delay: 0.4s;
    }
    
    @keyframes typing {
        0%, 60%, 100% {
            opacity: 0.3;
            transform: translateY(0);
        }
        30% {
            opacity: 1;
            transform: translateY(-10px);
        }
    }
</style>
""", unsafe_allow_html=True)

# Initialize session state for memory and chat management
if "chat_store" not in st.session_state:
    st.session_state.chat_store = load_chat_store()

if "chat_sessions" not in st.session_state:
    st.session_state.chat_sessions = load_chat_sessions()

if "current_session_id" not in st.session_state:
    # Create first session if none exist
    if not st.session_state.chat_sessions:
        new_session = create_new_session()
        st.session_state.chat_sessions[new_session["id"]] = new_session
        st.session_state.current_session_id = new_session["id"]
        save_chat_sessions(st.session_state.chat_sessions)
    else:
        # Use the most recent session
        latest_session = max(st.session_state.chat_sessions.items(),
                           key=lambda x: x[1]["last_updated"])
        st.session_state.current_session_id = latest_session[0]

if "memory" not in st.session_state:
    st.session_state.memory = Memory.from_defaults(
        session_id=st.session_state.current_session_id,
        token_limit=40000,
        chat_store=st.session_state.chat_store,
        chat_store_key=st.session_state.current_session_id
    )

# Initialize messages from memory
if "messages" not in st.session_state:
    # Try to load messages from memory
    try:
        chat_history = st.session_state.memory.get()
        if chat_history:
            st.session_state.messages = [
                {"role": msg.role, "content": msg.content} for msg in chat_history
            ]
        else:
            st.session_state.messages = []
            # Welcome message
            welcome_msg = {
                "role": "assistant",
                "content": "🙏 Namaste! I'm your Ayurvedic Health Assistant. I can help you with natural remedies, dietary advice, and holistic wellness tips based on ancient Ayurvedic wisdom. How may I assist you today?"
            }
            st.session_state.messages.append(welcome_msg)
            # Add welcome message to memory
            st.session_state.memory.put_messages([
                ChatMessage(role="assistant", content=welcome_msg["content"])
            ])
    except:
        st.session_state.messages = []
        # Welcome message
        welcome_msg = {
            "role": "assistant",
            "content": "🙏 Namaste! I'm your Ayurvedic Health Assistant. I can help you with natural remedies, dietary advice, and holistic wellness tips based on ancient Ayurvedic wisdom. How may I assist you today?"
        }
        st.session_state.messages.append(welcome_msg)

if "query_engine" not in st.session_state:
    with st.spinner("🌿 Loading Ayurvedic Knowledge Base..."):
        # Cache the embedding model
        @st.cache_resource
        def load_embedding_model():
            return FastEmbedEmbedding(
                model_name="BAAI/bge-small-en-v1.5",
                embed_batch_size=32,
                cache_dir="./embedding_cache"
            )
        
        Settings.embed_model = load_embedding_model()
        
        # Load FAISS index
        d = 384
        faiss_index = faiss.IndexFlatL2(d)
        
        # Load vector store
        vector_store = FaissVectorStore.from_persist_dir("faiss_db")
        storage_context = StorageContext.from_defaults(
            vector_store=vector_store, 
            persist_dir="faiss_db"
        )
        
        # Load index
        index = load_index_from_storage(
            storage_context=storage_context,
            index_id="2a3e044a-5744-41d0-9873-8d679b1571a8"
        )
        
        # Initialize LLM
        llm = Gemini(model="gemini-2.5-pro", api_key=os.environ.get("GOOGLE_API_KEY"))
        
        # Store LLM separately for direct chat
        st.session_state.llm = llm
        
        # Improved Ayurvedic prompt
        ayurveda_prompt_str = """You are an expert Ayurvedic physician. 

CRITICAL: Focus ONLY on what the user is currently asking about. If they ask about Apigenin, discuss Apigenin. If they ask about diabetes, discuss diabetes. DO NOT continue previous topics unless explicitly asked.

Context from knowledge base: {context_str}

Current Question: {query_str}

Provide a response following this structure when applicable:

**1. Definition/Information**
- What is it from an Ayurvedic perspective
- Key properties and characteristics
- Causes : what are the causes for the diseases
- Symptoms : if it's a condition

**2. Ayurvedic Treatment/Remedies** (if applicable)
- Herbal medicines with specific dosages
- Dietary recommendations
- Lifestyle modifications
- Yoga/pranayama practices
- Panchakarma therapies

**3. Benefits/Expected Outcomes**
- How it works in the body
- Expected results and timeline
- Scientific backing if available

Guidelines:
- Answer ONLY about the specific topic asked
- Be direct - no greetings or introductions
- Use bullet points for clarity
- Include Sanskrit names with translations
- Provide practical, actionable advice
- If asked about a compound/herb, focus on its properties and uses
- If the topic doesn't fit the structure, adapt accordingly
- End with healthcare consultation reminder for medical conditions

Remember: Each question is independent. Do not reference or continue from previous answers unless the user explicitly asks for elaboration."""
        
        ayurveda_prompt = PromptTemplate(ayurveda_prompt_str)
        
        # Create simple query engine WITHOUT chat memory
        st.session_state.query_engine = index.as_query_engine(
            llm=llm,
            similarity_top_k=3,
            text_qa_template=ayurveda_prompt
        )

# Function to check if query is a greeting or simple chat
def is_greeting_or_chat(query):
    greetings = ['hello', 'hi', 'hey', 'good morning', 'good afternoon', 'good evening', 
                 'namaste', 'how are you', "what's up", 'greetings', 'thank you', 'thanks',
                 'bye', 'goodbye', 'see you', 'ok', 'okay', 'yes', 'no']
    
    query_lower = query.lower().strip()
    
    # Check for exact matches or if query starts with greeting
    for greeting in greetings:
        if query_lower == greeting or query_lower.startswith(greeting):
            return True
    
    return False

# Function to generate greeting response
def get_greeting_response(query):
    query_lower = query.lower().strip()
    
    if 'hello' in query_lower or 'hi' in query_lower or 'hey' in query_lower:
        return "🙏 Namaste! How can I assist you with your health and wellness today? Feel free to ask me about Ayurvedic remedies, dietary advice, or any health concerns you may have."
    
    elif 'how are you' in query_lower:
        return "🌿 I'm here and ready to help you with your wellness journey! How are you feeling today? Is there any health concern or Ayurvedic topic you'd like to explore?"
    
    elif 'good morning' in query_lower:
        return "🌅 Good morning! Starting the day with awareness is wonderful. Would you like some Ayurvedic morning routine (Dinacharya) tips or help with any health questions?"
    
    elif 'good afternoon' in query_lower or 'good evening' in query_lower:
        return "🙏 Namaste! I hope you're having a peaceful day. How may I assist you with your health and wellness needs?"
    
    elif 'thank' in query_lower:
        return "🙏 You're most welcome! It's my pleasure to help. Is there anything else about Ayurveda or your health that you'd like to know?"
    
    elif 'bye' in query_lower or 'goodbye' in query_lower:
        return "🙏 Namaste! Take care of your health and well-being. Feel free to return anytime you need Ayurvedic guidance. Stay healthy!"
    
    else:
        return "🌿 I'm here to help! Please feel free to ask me any questions about Ayurvedic medicine, natural remedies, diet, or wellness practices."

# Function to check if query needs context from previous conversation
def is_follow_up_question(query):
    """Check if query is asking for elaboration on previous topic"""
    follow_up_words = ['elaborate', 'more about this', 'explain more', 'tell me more', 
                       'what else', 'continue', 'go on', 'expand on that']
    query_lower = query.lower()
    return any(phrase in query_lower for phrase in follow_up_words)

# Sidebar for chat history
with st.sidebar:
    st.markdown('<div class="sidebar-content">', unsafe_allow_html=True)

    # New Chat Button
    if st.button("➕ New Chat", key="new_chat", help="Start a new conversation"):
        # Create new session
        new_session = create_new_session()
        st.session_state.chat_sessions[new_session["id"]] = new_session
        st.session_state.current_session_id = new_session["id"]

        # Create new memory for the session
        st.session_state.memory = Memory.from_defaults(
            session_id=new_session["id"],
            token_limit=40000,
            chat_store=st.session_state.chat_store,
            chat_store_key=new_session["id"]
        )

        # Reset messages
        st.session_state.messages = []
        welcome_msg = {
            "role": "assistant",
            "content": "🙏 Namaste! I'm your Ayurvedic Health Assistant. I can help you with natural remedies, dietary advice, and holistic wellness tips based on ancient Ayurvedic wisdom. How may I assist you today?"
        }
        st.session_state.messages.append(welcome_msg)

        # Add welcome message to memory
        st.session_state.memory.put_messages([
            ChatMessage(role="assistant", content=welcome_msg["content"])
        ])

        # Save sessions
        save_chat_sessions(st.session_state.chat_sessions)
        save_chat_store(st.session_state.chat_store)
        st.rerun()

    st.markdown("### 💬 Chat History")

    # Display chat sessions
    if st.session_state.chat_sessions:
        # Sort sessions by last updated (most recent first)
        sorted_sessions = sorted(
            st.session_state.chat_sessions.items(),
            key=lambda x: x[1]["last_updated"],
            reverse=True
        )

        for session_id, session_data in sorted_sessions:
            # Create a container for each session
            is_active = session_id == st.session_state.current_session_id

            # Session button
            if st.button(
                f"📝 {session_data['title']}\n🕒 {session_data['created_at'][:16]}",
                key=f"session_{session_id}",
                help=f"Switch to chat: {session_data['title']}",
                use_container_width=True
            ):
                # Switch to this session
                st.session_state.current_session_id = session_id

                # Load memory for this session
                st.session_state.memory = Memory.from_defaults(
                    session_id=session_id,
                    token_limit=40000,
                    chat_store=st.session_state.chat_store,
                    chat_store_key=session_id
                )

                # Load messages from memory
                try:
                    chat_history = st.session_state.memory.get()
                    if chat_history:
                        st.session_state.messages = [
                            {"role": msg.role, "content": msg.content} for msg in chat_history
                        ]
                    else:
                        st.session_state.messages = []
                except:
                    st.session_state.messages = []

                st.rerun()

    st.markdown('</div>', unsafe_allow_html=True)

# Main chat area
col1, col2 = st.columns([3, 1])

with col1:
    # Chat header
    st.markdown("""
    <div class="chat-header">
        <div class="header-avatar">🌿</div>
        <div class="header-info">
            <h2>Ayurvedic Health Assistant</h2>
            <p>Online • Ancient wisdom for modern wellness</p>
        </div>
    </div>
    """, unsafe_allow_html=True)

    # Chat messages display
    for message in st.session_state.messages:
        with st.chat_message(message["role"], avatar="🧑" if message["role"] == "user" else "🌿"):
            st.write(message["content"])

    # Chat input
    if prompt := st.chat_input("Type your health question..."):
        # Update session title if this is the first user message
        if len(st.session_state.messages) <= 1:  # Only welcome message exists
            update_session_title(st.session_state.chat_sessions, st.session_state.current_session_id, prompt)
            save_chat_sessions(st.session_state.chat_sessions)

        # Add user message to session state
        user_message = {
            "role": "user",
            "content": prompt
        }
        st.session_state.messages.append(user_message)

        # Add user message to memory
        st.session_state.memory.put_messages([
            ChatMessage(role="user", content=prompt)
        ])

        # Display user message
        with st.chat_message("user", avatar="🧑"):
            st.write(prompt)
    
    # Show typing indicator
    with st.chat_message("assistant", avatar="🌿"):
        typing_placeholder = st.empty()
        typing_placeholder.markdown("""
        <div class="typing-indicator">
            <div class="typing-dots">
                <span></span>
                <span></span>
                <span></span>
            </div>
            <span>Vaidya is typing...</span>
        </div>
        """, unsafe_allow_html=True)
        
        try:
            # Check if it's a greeting or simple chat
            if is_greeting_or_chat(prompt):
                # Use direct response for greetings
                assistant_response = get_greeting_response(prompt)
            elif is_follow_up_question(prompt) and len(st.session_state.messages) > 2:
                # For follow-up questions, add context from previous exchange
                prev_question = ""
                for i in range(len(st.session_state.messages) - 2, -1, -1):
                    if st.session_state.messages[i]["role"] == "user":
                        prev_question = st.session_state.messages[i]["content"]
                        break
                
                # Combine previous question with current request
                enhanced_query = f"Previous question was: {prev_question}. Now the user asks: {prompt}"
                response = st.session_state.query_engine.query(enhanced_query)
                assistant_response = str(response)
            else:
                # Use simple query engine for health-related queries
                response = st.session_state.query_engine.query(prompt)
                assistant_response = str(response)
            
            # Clear typing indicator and show response
            typing_placeholder.empty()
            st.write(assistant_response)

            # Add assistant message to session state
            assistant_message = {
                "role": "assistant",
                "content": assistant_response
            }
            st.session_state.messages.append(assistant_message)

            # Add assistant message to memory
            st.session_state.memory.put_messages([
                ChatMessage(role="assistant", content=assistant_response)
            ])

            # Save chat store and sessions
            save_chat_store(st.session_state.chat_store)

            # Update session timestamp
            if st.session_state.current_session_id in st.session_state.chat_sessions:
                st.session_state.chat_sessions[st.session_state.current_session_id]["last_updated"] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                save_chat_sessions(st.session_state.chat_sessions)
            
        except Exception as e:
            typing_placeholder.empty()
            error_msg = f"I apologize, but I encountered an error: {str(e)}"
            st.error(error_msg)

            # Add error message to session state
            error_message = {
                "role": "assistant",
                "content": error_msg
            }
            st.session_state.messages.append(error_message)

            # Add error message to memory
            st.session_state.memory.put_messages([
                ChatMessage(role="assistant", content=error_msg)
            ])

            # Save chat store
            save_chat_store(st.session_state.chat_store)

# Add a second column for additional features (optional)
with col2:
    st.markdown("### 🔧 Chat Options")

    # Clear current chat button
    if st.button("🗑️ Clear Current Chat", help="Clear the current conversation"):
        # Clear messages
        st.session_state.messages = []

        # Add welcome message
        welcome_msg = {
            "role": "assistant",
            "content": "🙏 Namaste! I'm your Ayurvedic Health Assistant. I can help you with natural remedies, dietary advice, and holistic wellness tips based on ancient Ayurvedic wisdom. How may I assist you today?"
        }
        st.session_state.messages.append(welcome_msg)

        # Clear memory for current session
        st.session_state.memory = Memory.from_defaults(
            session_id=st.session_state.current_session_id,
            token_limit=40000,
            chat_store=SimpleChatStore(),  # New empty chat store for this session
            chat_store_key=st.session_state.current_session_id
        )

        # Add welcome message to memory
        st.session_state.memory.put_messages([
            ChatMessage(role="assistant", content=welcome_msg["content"])
        ])

        st.rerun()

    # Export chat button
    if st.button("📥 Export Chat", help="Export current conversation"):
        chat_text = ""
        for msg in st.session_state.messages:
            role = "You" if msg["role"] == "user" else "Assistant"
            chat_text += f"{role}: {msg['content']}\n\n"

        st.download_button(
            label="Download Chat History",
            data=chat_text,
            file_name=f"ayurveda_chat_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt",
            mime="text/plain"
        )

    # Session info
    if st.session_state.current_session_id in st.session_state.chat_sessions:
        session_info = st.session_state.chat_sessions[st.session_state.current_session_id]
        st.markdown("### 📊 Session Info")
        st.write(f"**Created:** {session_info['created_at']}")
        st.write(f"**Updated:** {session_info['last_updated']}")
        st.write(f"**Messages:** {len(st.session_state.messages)}")
